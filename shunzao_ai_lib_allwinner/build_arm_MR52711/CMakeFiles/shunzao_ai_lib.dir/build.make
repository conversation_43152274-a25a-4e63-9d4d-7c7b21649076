# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527

# Include any dependencies generated for this target.
include CMakeFiles/shunzao_ai_lib.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/shunzao_ai_lib.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/shunzao_ai_lib.dir/flags.make

CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp.o: CMakeFiles/shunzao_ai_lib.dir/flags.make
CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp.o: /root/pan/shunzao_ai_lib-develop/common/npu_util.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp.o"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp.o -c /root/pan/shunzao_ai_lib-develop/common/npu_util.cpp

CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp.i"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/common/npu_util.cpp > CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp.i

CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp.s"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/common/npu_util.cpp -o CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp.s

CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npulib.cpp.o: CMakeFiles/shunzao_ai_lib.dir/flags.make
CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npulib.cpp.o: /root/pan/shunzao_ai_lib-develop/common/npulib.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npulib.cpp.o"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npulib.cpp.o -c /root/pan/shunzao_ai_lib-develop/common/npulib.cpp

CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npulib.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npulib.cpp.i"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/common/npulib.cpp > CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npulib.cpp.i

CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npulib.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npulib.cpp.s"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/common/npulib.cpp -o CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npulib.cpp.s

CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.o: CMakeFiles/shunzao_ai_lib.dir/flags.make
CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.o: ../src/algorithm/line_decode.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.o"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.o -c /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/line_decode.cc

CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.i"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/line_decode.cc > CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.i

CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.s"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/line_decode.cc -o CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.s

CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.o: CMakeFiles/shunzao_ai_lib.dir/flags.make
CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.o: ../src/algorithm/yolov5.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.o"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.o -c /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/yolov5.cc

CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.i"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/yolov5.cc > CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.i

CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.s"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/yolov5.cc -o CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.s

CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.o: CMakeFiles/shunzao_ai_lib.dir/flags.make
CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.o: ../src/algorithm/yolov8.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.o"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.o -c /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/yolov8.cc

CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.i"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/yolov8.cc > CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.i

CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.s"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/yolov8.cc -o CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.s

CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.o: CMakeFiles/shunzao_ai_lib.dir/flags.make
CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.o: ../src/basic_model/base_model.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.o"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.o -c /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/basic_model/base_model.cc

CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.i"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/basic_model/base_model.cc > CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.i

CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.s"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/basic_model/base_model.cc -o CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.s

CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.o: CMakeFiles/shunzao_ai_lib.dir/flags.make
CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.o: ../src/shunzao_ai_task.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.o"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.o -c /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/shunzao_ai_task.cc

CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.i"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/shunzao_ai_task.cc > CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.i

CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.s"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/shunzao_ai_task.cc -o CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.s

CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.o: CMakeFiles/shunzao_ai_lib.dir/flags.make
CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.o: ../src/task/ground_det.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.o"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.o -c /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/task/ground_det.cc

CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.i"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/task/ground_det.cc > CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.i

CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.s"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/task/ground_det.cc -o CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.s

CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.o: CMakeFiles/shunzao_ai_lib.dir/flags.make
CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.o: ../src/task/line_det.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.o"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.o -c /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/task/line_det.cc

CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.i"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/task/line_det.cc > CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.i

CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.s"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/task/line_det.cc -o CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.s

CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.o: CMakeFiles/shunzao_ai_lib.dir/flags.make
CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.o: ../src/task/slam_det.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.o"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.o -c /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/task/slam_det.cc

CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.i"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/task/slam_det.cc > CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.i

CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.s"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/task/slam_det.cc -o CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.s

CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.o: CMakeFiles/shunzao_ai_lib.dir/flags.make
CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.o: ../src/utils/utils.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.o"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.o -c /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/utils/utils.cc

CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.i"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/utils/utils.cc > CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.i

CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.s"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/utils/utils.cc -o CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.s

CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.o: CMakeFiles/shunzao_ai_lib.dir/flags.make
CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.o: ../src/algorithm/yolov5s_pre.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.o"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.o -c /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/yolov5s_pre.cpp

CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.i"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/yolov5s_pre.cpp > CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.i

CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.s"
	/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ --sysroot=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/aarch64-none-linux-gnu/libc $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/yolov5s_pre.cpp -o CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.s

# Object files for target shunzao_ai_lib
shunzao_ai_lib_OBJECTS = \
"CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp.o" \
"CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npulib.cpp.o" \
"CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.o" \
"CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.o" \
"CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.o" \
"CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.o" \
"CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.o" \
"CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.o" \
"CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.o" \
"CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.o" \
"CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.o" \
"CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.o"

# External object files for target shunzao_ai_lib
shunzao_ai_lib_EXTERNAL_OBJECTS =

libshunzao_ai_lib.so: CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp.o
libshunzao_ai_lib.so: CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npulib.cpp.o
libshunzao_ai_lib.so: CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.o
libshunzao_ai_lib.so: CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.o
libshunzao_ai_lib.so: CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.o
libshunzao_ai_lib.so: CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.o
libshunzao_ai_lib.so: CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.o
libshunzao_ai_lib.so: CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.o
libshunzao_ai_lib.so: CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.o
libshunzao_ai_lib.so: CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.o
libshunzao_ai_lib.so: CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.o
libshunzao_ai_lib.so: CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.o
libshunzao_ai_lib.so: CMakeFiles/shunzao_ai_lib.dir/build.make
libshunzao_ai_lib.so: ../../common/lib_linux_aarch64/MR527/libVIPlite.so
libshunzao_ai_lib.so: ../../common/lib_linux_aarch64/MR527/libVIPuser.so
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_core.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_imgproc.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_imgcodecs.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_highgui.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_videoio.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_imgcodecs.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_imgproc.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/libopencv_core.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/libittnotify.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/liblibjpeg-turbo.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/liblibwebp.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/liblibpng.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/liblibtiff.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/libzlib.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/liblibopenjp2.a
libshunzao_ai_lib.so: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/lib/opencv4/3rdparty/libtegra_hal.a
libshunzao_ai_lib.so: CMakeFiles/shunzao_ai_lib.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Linking CXX shared library libshunzao_ai_lib.so"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/shunzao_ai_lib.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/shunzao_ai_lib.dir/build: libshunzao_ai_lib.so

.PHONY : CMakeFiles/shunzao_ai_lib.dir/build

CMakeFiles/shunzao_ai_lib.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/shunzao_ai_lib.dir/cmake_clean.cmake
.PHONY : CMakeFiles/shunzao_ai_lib.dir/clean

CMakeFiles/shunzao_ai_lib.dir/depend:
	cd /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527 && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527 /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527 /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles/shunzao_ai_lib.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/shunzao_ai_lib.dir/depend

