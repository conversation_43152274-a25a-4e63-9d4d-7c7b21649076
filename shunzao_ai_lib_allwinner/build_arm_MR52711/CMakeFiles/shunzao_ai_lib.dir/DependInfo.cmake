# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp" "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npu_util.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/common/npulib.cpp" "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles/shunzao_ai_lib.dir/root/pan/shunzao_ai_lib-develop/common/npulib.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/line_decode.cc" "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.o"
  "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/yolov5.cc" "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.o"
  "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/yolov5s_pre.cpp" "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/algorithm/yolov8.cc" "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.o"
  "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/basic_model/base_model.cc" "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.o"
  "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/shunzao_ai_task.cc" "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.o"
  "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/task/ground_det.cc" "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.o"
  "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/task/line_det.cc" "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.o"
  "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/task/slam_det.cc" "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.o"
  "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/src/utils/utils.cc" "/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_GLIBCXX_USE_CXX11_ABI=1"
  "shunzao_ai_lib_EXPORTS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "../../common/include"
  "../../common"
  "../include"
  "../include/algorithm"
  "../include/utils"
  "../include/basic_model"
  "../include/task"
  "/root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
