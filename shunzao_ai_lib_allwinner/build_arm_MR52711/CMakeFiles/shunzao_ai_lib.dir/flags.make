# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# compile CXX with /root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++
CXX_FLAGS =  -Wall -Wextra -O2 -fopenmp -fdiagnostics-color=always -fPIC   -D_GLIBCXX_USE_CXX11_ABI=0 -std=gnu++11

CXX_DEFINES = -D_GLIBCXX_USE_CXX11_ABI=1 -Dshunzao_ai_lib_EXPORTS

CXX_INCLUDES = -I/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../common/include -I/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/../common -I/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/include -I/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/include/algorithm -I/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/include/utils -I/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/include/basic_model -I/root/pan/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/include/task -isystem /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4 

