# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/panpan/code/shunzao_ai_lib-develop/shunzao_ai_lib_allwinner/build_arm_MR527/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named shunzao_ai_lib

# Build rule for target.
shunzao_ai_lib: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shunzao_ai_lib
.PHONY : shunzao_ai_lib

# fast build rule for target.
shunzao_ai_lib/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/build
.PHONY : shunzao_ai_lib/fast

#=============================================================================
# Target rules for targets named shunzao_ai_demo

# Build rule for target.
shunzao_ai_demo: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shunzao_ai_demo
.PHONY : shunzao_ai_demo

# fast build rule for target.
shunzao_ai_demo/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_demo.dir/build.make CMakeFiles/shunzao_ai_demo.dir/build
.PHONY : shunzao_ai_demo/fast

home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.o: home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp.o
.PHONY : home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.o

# target to build an object file
home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/home/<USER>/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp.o
.PHONY : home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp.o

home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.i: home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp.i
.PHONY : home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.i

# target to preprocess a source file
home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/home/<USER>/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp.i
.PHONY : home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp.i

home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.s: home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp.s
.PHONY : home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.s

# target to generate assembly for a file
home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/home/<USER>/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp.s
.PHONY : home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.cpp.s

home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.o: home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp.o
.PHONY : home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.o

# target to build an object file
home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/home/<USER>/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp.o
.PHONY : home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp.o

home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.i: home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp.i
.PHONY : home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.i

# target to preprocess a source file
home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/home/<USER>/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp.i
.PHONY : home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp.i

home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.s: home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp.s
.PHONY : home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.s

# target to generate assembly for a file
home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/home/<USER>/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp.s
.PHONY : home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.cpp.s

main.o: main.cc.o
.PHONY : main.o

# target to build an object file
main.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_demo.dir/build.make CMakeFiles/shunzao_ai_demo.dir/main.cc.o
.PHONY : main.cc.o

main.i: main.cc.i
.PHONY : main.i

# target to preprocess a source file
main.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_demo.dir/build.make CMakeFiles/shunzao_ai_demo.dir/main.cc.i
.PHONY : main.cc.i

main.s: main.cc.s
.PHONY : main.s

# target to generate assembly for a file
main.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_demo.dir/build.make CMakeFiles/shunzao_ai_demo.dir/main.cc.s
.PHONY : main.cc.s

src/algorithm/line_decode.o: src/algorithm/line_decode.cc.o
.PHONY : src/algorithm/line_decode.o

# target to build an object file
src/algorithm/line_decode.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.o
.PHONY : src/algorithm/line_decode.cc.o

src/algorithm/line_decode.i: src/algorithm/line_decode.cc.i
.PHONY : src/algorithm/line_decode.i

# target to preprocess a source file
src/algorithm/line_decode.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.i
.PHONY : src/algorithm/line_decode.cc.i

src/algorithm/line_decode.s: src/algorithm/line_decode.cc.s
.PHONY : src/algorithm/line_decode.s

# target to generate assembly for a file
src/algorithm/line_decode.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/algorithm/line_decode.cc.s
.PHONY : src/algorithm/line_decode.cc.s

src/algorithm/yolov5.o: src/algorithm/yolov5.cc.o
.PHONY : src/algorithm/yolov5.o

# target to build an object file
src/algorithm/yolov5.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.o
.PHONY : src/algorithm/yolov5.cc.o

src/algorithm/yolov5.i: src/algorithm/yolov5.cc.i
.PHONY : src/algorithm/yolov5.i

# target to preprocess a source file
src/algorithm/yolov5.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.i
.PHONY : src/algorithm/yolov5.cc.i

src/algorithm/yolov5.s: src/algorithm/yolov5.cc.s
.PHONY : src/algorithm/yolov5.s

# target to generate assembly for a file
src/algorithm/yolov5.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5.cc.s
.PHONY : src/algorithm/yolov5.cc.s

src/algorithm/yolov5s_pre.o: src/algorithm/yolov5s_pre.cpp.o
.PHONY : src/algorithm/yolov5s_pre.o

# target to build an object file
src/algorithm/yolov5s_pre.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.o
.PHONY : src/algorithm/yolov5s_pre.cpp.o

src/algorithm/yolov5s_pre.i: src/algorithm/yolov5s_pre.cpp.i
.PHONY : src/algorithm/yolov5s_pre.i

# target to preprocess a source file
src/algorithm/yolov5s_pre.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.i
.PHONY : src/algorithm/yolov5s_pre.cpp.i

src/algorithm/yolov5s_pre.s: src/algorithm/yolov5s_pre.cpp.s
.PHONY : src/algorithm/yolov5s_pre.s

# target to generate assembly for a file
src/algorithm/yolov5s_pre.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov5s_pre.cpp.s
.PHONY : src/algorithm/yolov5s_pre.cpp.s

src/algorithm/yolov8.o: src/algorithm/yolov8.cc.o
.PHONY : src/algorithm/yolov8.o

# target to build an object file
src/algorithm/yolov8.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.o
.PHONY : src/algorithm/yolov8.cc.o

src/algorithm/yolov8.i: src/algorithm/yolov8.cc.i
.PHONY : src/algorithm/yolov8.i

# target to preprocess a source file
src/algorithm/yolov8.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.i
.PHONY : src/algorithm/yolov8.cc.i

src/algorithm/yolov8.s: src/algorithm/yolov8.cc.s
.PHONY : src/algorithm/yolov8.s

# target to generate assembly for a file
src/algorithm/yolov8.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/algorithm/yolov8.cc.s
.PHONY : src/algorithm/yolov8.cc.s

src/basic_model/base_model.o: src/basic_model/base_model.cc.o
.PHONY : src/basic_model/base_model.o

# target to build an object file
src/basic_model/base_model.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.o
.PHONY : src/basic_model/base_model.cc.o

src/basic_model/base_model.i: src/basic_model/base_model.cc.i
.PHONY : src/basic_model/base_model.i

# target to preprocess a source file
src/basic_model/base_model.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.i
.PHONY : src/basic_model/base_model.cc.i

src/basic_model/base_model.s: src/basic_model/base_model.cc.s
.PHONY : src/basic_model/base_model.s

# target to generate assembly for a file
src/basic_model/base_model.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/basic_model/base_model.cc.s
.PHONY : src/basic_model/base_model.cc.s

src/shunzao_ai_task.o: src/shunzao_ai_task.cc.o
.PHONY : src/shunzao_ai_task.o

# target to build an object file
src/shunzao_ai_task.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.o
.PHONY : src/shunzao_ai_task.cc.o

src/shunzao_ai_task.i: src/shunzao_ai_task.cc.i
.PHONY : src/shunzao_ai_task.i

# target to preprocess a source file
src/shunzao_ai_task.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.i
.PHONY : src/shunzao_ai_task.cc.i

src/shunzao_ai_task.s: src/shunzao_ai_task.cc.s
.PHONY : src/shunzao_ai_task.s

# target to generate assembly for a file
src/shunzao_ai_task.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/shunzao_ai_task.cc.s
.PHONY : src/shunzao_ai_task.cc.s

src/task/ground_det.o: src/task/ground_det.cc.o
.PHONY : src/task/ground_det.o

# target to build an object file
src/task/ground_det.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.o
.PHONY : src/task/ground_det.cc.o

src/task/ground_det.i: src/task/ground_det.cc.i
.PHONY : src/task/ground_det.i

# target to preprocess a source file
src/task/ground_det.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.i
.PHONY : src/task/ground_det.cc.i

src/task/ground_det.s: src/task/ground_det.cc.s
.PHONY : src/task/ground_det.s

# target to generate assembly for a file
src/task/ground_det.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/task/ground_det.cc.s
.PHONY : src/task/ground_det.cc.s

src/task/line_det.o: src/task/line_det.cc.o
.PHONY : src/task/line_det.o

# target to build an object file
src/task/line_det.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.o
.PHONY : src/task/line_det.cc.o

src/task/line_det.i: src/task/line_det.cc.i
.PHONY : src/task/line_det.i

# target to preprocess a source file
src/task/line_det.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.i
.PHONY : src/task/line_det.cc.i

src/task/line_det.s: src/task/line_det.cc.s
.PHONY : src/task/line_det.s

# target to generate assembly for a file
src/task/line_det.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/task/line_det.cc.s
.PHONY : src/task/line_det.cc.s

src/task/slam_det.o: src/task/slam_det.cc.o
.PHONY : src/task/slam_det.o

# target to build an object file
src/task/slam_det.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.o
.PHONY : src/task/slam_det.cc.o

src/task/slam_det.i: src/task/slam_det.cc.i
.PHONY : src/task/slam_det.i

# target to preprocess a source file
src/task/slam_det.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.i
.PHONY : src/task/slam_det.cc.i

src/task/slam_det.s: src/task/slam_det.cc.s
.PHONY : src/task/slam_det.s

# target to generate assembly for a file
src/task/slam_det.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/task/slam_det.cc.s
.PHONY : src/task/slam_det.cc.s

src/utils/utils.o: src/utils/utils.cc.o
.PHONY : src/utils/utils.o

# target to build an object file
src/utils/utils.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.o
.PHONY : src/utils/utils.cc.o

src/utils/utils.i: src/utils/utils.cc.i
.PHONY : src/utils/utils.i

# target to preprocess a source file
src/utils/utils.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.i
.PHONY : src/utils/utils.cc.i

src/utils/utils.s: src/utils/utils.cc.s
.PHONY : src/utils/utils.s

# target to generate assembly for a file
src/utils/utils.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/shunzao_ai_lib.dir/build.make CMakeFiles/shunzao_ai_lib.dir/src/utils/utils.cc.s
.PHONY : src/utils/utils.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... shunzao_ai_demo"
	@echo "... shunzao_ai_lib"
	@echo "... home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.o"
	@echo "... home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.i"
	@echo "... home/pan/panpan/code/shunzao_ai_lib-develop/common/npu_util.s"
	@echo "... home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.o"
	@echo "... home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.i"
	@echo "... home/pan/panpan/code/shunzao_ai_lib-develop/common/npulib.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... src/algorithm/line_decode.o"
	@echo "... src/algorithm/line_decode.i"
	@echo "... src/algorithm/line_decode.s"
	@echo "... src/algorithm/yolov5.o"
	@echo "... src/algorithm/yolov5.i"
	@echo "... src/algorithm/yolov5.s"
	@echo "... src/algorithm/yolov5s_pre.o"
	@echo "... src/algorithm/yolov5s_pre.i"
	@echo "... src/algorithm/yolov5s_pre.s"
	@echo "... src/algorithm/yolov8.o"
	@echo "... src/algorithm/yolov8.i"
	@echo "... src/algorithm/yolov8.s"
	@echo "... src/basic_model/base_model.o"
	@echo "... src/basic_model/base_model.i"
	@echo "... src/basic_model/base_model.s"
	@echo "... src/shunzao_ai_task.o"
	@echo "... src/shunzao_ai_task.i"
	@echo "... src/shunzao_ai_task.s"
	@echo "... src/task/ground_det.o"
	@echo "... src/task/ground_det.i"
	@echo "... src/task/ground_det.s"
	@echo "... src/task/line_det.o"
	@echo "... src/task/line_det.i"
	@echo "... src/task/line_det.s"
	@echo "... src/task/slam_det.o"
	@echo "... src/task/slam_det.i"
	@echo "... src/task/slam_det.s"
	@echo "... src/utils/utils.o"
	@echo "... src/utils/utils.i"
	@echo "... src/utils/utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

